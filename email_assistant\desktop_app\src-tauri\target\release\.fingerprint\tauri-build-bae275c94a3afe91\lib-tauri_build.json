{"rustc": 1842507548689473721, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 1369601567987815722, "path": 14484331888355962222, "deps": [[2671782512663819132, "tauri_utils", false, 11132625988936367240], [4899080583175475170, "semver", false, 10438149423428263558], [6913375703034175521, "schemars", false, 5220195266555608873], [7170110829644101142, "json_patch", false, 3898244098352474111], [9689903380558560274, "serde", false, 101596170299597295], [12714016054753183456, "tauri_winres", false, 13550629551011197611], [13077543566650298139, "heck", false, 1577398889775765422], [13475171727366188400, "cargo_toml", false, 2477464570302506030], [13625485746686963219, "anyhow", false, 17279382658687598212], [15367738274754116744, "serde_json", false, 10713812322005748657], [15609422047640926750, "toml", false, 900546928040642840], [15622660310229662834, "walkdir", false, 4267858985654455119], [16928111194414003569, "dirs", false, 1681771403133517944], [17155886227862585100, "glob", false, 12384589947144232082]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-build-bae275c94a3afe91\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}