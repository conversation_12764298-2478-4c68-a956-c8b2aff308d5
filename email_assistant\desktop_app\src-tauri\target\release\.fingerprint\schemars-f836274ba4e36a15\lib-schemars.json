{"rustc": 1842507548689473721, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 1369601567987815722, "path": 1660944289106784126, "deps": [[3150220818285335163, "url", false, 14894005264751682384], [6913375703034175521, "build_script_build", false, 17104150003680432926], [8319709847752024821, "uuid1", false, 14754163599292717405], [9122563107207267705, "dyn_clone", false, 3155010584954735503], [9689903380558560274, "serde", false, 101596170299597295], [14923790796823607459, "indexmap", false, 5085022646292142201], [15367738274754116744, "serde_json", false, 10713812322005748657], [16071897500792579091, "schemars_derive", false, 14216704234455091809]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\schemars-f836274ba4e36a15\\dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}