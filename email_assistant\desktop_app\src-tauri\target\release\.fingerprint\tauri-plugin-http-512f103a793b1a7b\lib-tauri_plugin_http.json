{"rustc": 1842507548689473721, "features": "[\"charset\", \"cookies\", \"default\", \"http2\", \"macos-system-configuration\", \"rustls-tls\"]", "declared_features": "[\"blocking\", \"brotli\", \"charset\", \"cookies\", \"dangerous-settings\", \"default\", \"deflate\", \"gzip\", \"http2\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"socks\", \"stream\", \"tracing\", \"trust-dns\", \"unsafe-headers\", \"zstd\"]", "target": 7795770796983219439, "profile": 2040997289075261528, "path": 17331047242322728479, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 8092876893493829581], [3150220818285335163, "url", false, 11654752898637132681], [6416823254013318197, "tauri_plugin_fs", false, 689430668201097063], [7085222851776090619, "reqwest", false, 14053785665877409833], [8298091525883606470, "cookie_store", false, 4445271584190846920], [9010263965687315507, "http", false, 1862789691165185595], [9451456094439810778, "regex", false, 6287572008168542260], [9689903380558560274, "serde", false, 9841288889961963921], [10806645703491011684, "thiserror", false, 13847597657789702417], [12393800526703971956, "tokio", false, 1417906539048028063], [14039947826026167952, "tauri", false, 10827897968400736768], [15367738274754116744, "serde_json", false, 13225267877164477003], [16066129441945555748, "bytes", false, 5746740623261701503], [16171925541490437305, "build_script_build", false, 790388759466741103], [17047088963840213854, "data_url", false, 9770199703145872237]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-plugin-http-512f103a793b1a7b\\dep-lib-tauri_plugin_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}