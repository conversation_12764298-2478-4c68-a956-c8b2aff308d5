{"rustc": 1842507548689473721, "features": "[\"alloc\", \"derive\"]", "declared_features": "[\"alloc\", \"default\", \"derive\"]", "target": 723370850876025358, "profile": 1369601567987815722, "path": 5661795991871761775, "deps": [[4022439902832367970, "zerofrom_derive", false, 11998117434376638686]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\zerofrom-5e6e284c53aedc6f\\dep-lib-zerofrom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}