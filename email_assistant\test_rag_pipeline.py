#!/usr/bin/env python3
"""
Test script for the RAG pipeline

This script tests the complete RAG pipeline by:
1. Starting the RAG service
2. Sending a test draft generation request
3. Verifying the response
"""

import asyncio
import httpx
import json
import sys
import time

async def test_rag_service():
    """Test the RAG service draft generation"""
    
    # Test data
    test_request = {
        "subject": "Meeting Request for Project Discussion",
        "sender": "<EMAIL>",
        "content": "Hi, I would like to schedule a meeting to discuss the upcoming project timeline and deliverables. Are you available next week?"
    }
    
    rag_service_url = "http://localhost:8003"
    
    print("🧪 Testing RAG Service...")
    print(f"📡 RAG Service URL: {rag_service_url}")
    print(f"📧 Test Email Subject: {test_request['subject']}")
    print(f"👤 Test Email Sender: {test_request['sender']}")
    print()
    
    async with httpx.AsyncClient(timeout=60.0) as client:
        try:
            # Test health endpoint
            print("1️⃣ Testing health endpoint...")
            health_response = await client.get(f"{rag_service_url}/health")
            if health_response.status_code == 200:
                health_data = health_response.json()
                print(f"✅ Health check passed: {health_data}")
            else:
                print(f"❌ Health check failed: {health_response.status_code}")
                return False
            
            print()
            
            # Test root endpoint
            print("2️⃣ Testing root endpoint...")
            root_response = await client.get(f"{rag_service_url}/")
            if root_response.status_code == 200:
                root_data = root_response.json()
                print(f"✅ Root endpoint: {root_data}")
            else:
                print(f"❌ Root endpoint failed: {root_response.status_code}")
                return False
            
            print()
            
            # Test draft generation
            print("3️⃣ Testing draft generation...")
            print("⏳ Generating draft (this may take a moment)...")
            
            draft_response = await client.post(
                f"{rag_service_url}/generate_draft",
                json=test_request
            )
            
            if draft_response.status_code == 200:
                draft_data = draft_response.json()
                print("✅ Draft generation successful!")
                print(f"📝 Generated Draft:")
                print("-" * 50)
                print(draft_data.get('draft', 'No draft content'))
                print("-" * 50)
                print(f"🔍 Context emails used: {draft_data.get('context_emails_count', 0)}")
                print(f"📊 Metadata: {draft_data.get('metadata', {})}")
                return True
            else:
                print(f"❌ Draft generation failed: {draft_response.status_code}")
                try:
                    error_data = draft_response.json()
                    print(f"Error details: {error_data}")
                except:
                    print(f"Error text: {draft_response.text}")
                return False
                
        except httpx.ConnectError:
            print(f"❌ Cannot connect to RAG service at {rag_service_url}")
            print("💡 Make sure the RAG service is running:")
            print("   cd email_assistant/rag_service")
            print("   python main.py")
            return False
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            return False

async def test_ingestion_service():
    """Test if ingestion service is available"""
    ingestion_url = "http://localhost:8080"
    
    print("🗄️ Testing Ingestion Service...")
    print(f"📡 Ingestion Service URL: {ingestion_url}")
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            health_response = await client.get(f"{ingestion_url}/health")
            if health_response.status_code == 200:
                health_data = health_response.json()
                print(f"✅ Ingestion service healthy: {health_data}")
                return True
            else:
                print(f"⚠️ Ingestion service not healthy: {health_response.status_code}")
                return False
        except httpx.ConnectError:
            print(f"❌ Cannot connect to ingestion service at {ingestion_url}")
            print("💡 Make sure the ingestion service is running:")
            print("   cd email_assistant/ingestion_service")
            print("   python main.py")
            return False

async def test_ollama_service():
    """Test if Ollama service is available"""
    ollama_url = "http://localhost:11434"
    
    print("🦙 Testing Ollama Service...")
    print(f"📡 Ollama Service URL: {ollama_url}")
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            # Test if Ollama is running
            response = await client.get(f"{ollama_url}/api/tags")
            if response.status_code == 200:
                models = response.json()
                print(f"✅ Ollama service running")
                print(f"📋 Available models: {[model['name'] for model in models.get('models', [])]}")
                return True
            else:
                print(f"⚠️ Ollama service responded with: {response.status_code}")
                return False
        except httpx.ConnectError:
            print(f"❌ Cannot connect to Ollama service at {ollama_url}")
            print("💡 Make sure Ollama is running:")
            print("   ollama serve")
            return False

async def main():
    """Main test function"""
    print("🚀 RAG Pipeline Integration Test")
    print("=" * 50)
    print()
    
    # Test all services
    ollama_ok = await test_ollama_service()
    print()
    
    ingestion_ok = await test_ingestion_service()
    print()
    
    if not ollama_ok:
        print("❌ Ollama service is required for RAG functionality")
        return False
    
    if not ingestion_ok:
        print("⚠️ Ingestion service is not available - RAG will work with limited context")
        print()
    
    # Test RAG service
    rag_ok = await test_rag_service()
    print()
    
    if rag_ok:
        print("🎉 RAG Pipeline test completed successfully!")
        print("✅ The system is ready for email draft generation")
        return True
    else:
        print("❌ RAG Pipeline test failed")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
