{"rustc": 1842507548689473721, "features": "[\"charset\", \"cookies\", \"default\", \"http2\", \"macos-system-configuration\", \"rustls-tls\"]", "declared_features": "[\"blocking\", \"brotli\", \"charset\", \"cookies\", \"dangerous-settings\", \"default\", \"deflate\", \"gzip\", \"http2\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"socks\", \"stream\", \"tracing\", \"trust-dns\", \"unsafe-headers\", \"zstd\"]", "target": 5408242616063297496, "profile": 1369601567987815722, "path": 3686219659668893688, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 9818158836129114236], [3150220818285335163, "url", false, 14894005264751682384], [6913375703034175521, "schemars", false, 5220195266555608873], [9451456094439810778, "regex", false, 2525954231128846445], [9689903380558560274, "serde", false, 101596170299597295], [14542658604911247535, "tauri_plugin", false, 5057791224111139097]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-plugin-http-d4161d53f879adc0\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}