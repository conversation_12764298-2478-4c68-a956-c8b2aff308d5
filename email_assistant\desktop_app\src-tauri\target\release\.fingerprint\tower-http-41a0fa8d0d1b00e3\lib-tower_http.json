{"rustc": 1842507548689473721, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 2040997289075261528, "path": 9958066978812107341, "deps": [[784494742817713399, "tower_service", false, 5062019019135106002], [1906322745568073236, "pin_project_lite", false, 2389057907062384012], [4121350475192885151, "iri_string", false, 10653739339318347094], [5695049318159433696, "tower", false, 11764452345109143343], [7712452662827335977, "tower_layer", false, 4087051148562280967], [7896293946984509699, "bitflags", false, 4405188476997761181], [9010263965687315507, "http", false, 1862789691165185595], [10629569228670356391, "futures_util", false, 12982142533534609786], [14084095096285906100, "http_body", false, 15356474674297432158], [16066129441945555748, "bytes", false, 5746740623261701503]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tower-http-41a0fa8d0d1b00e3\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}