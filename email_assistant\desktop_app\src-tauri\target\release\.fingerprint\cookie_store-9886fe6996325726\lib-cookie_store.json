{"rustc": 1842507548689473721, "features": "[\"default\", \"public_suffix\", \"serde\", \"serde_json\"]", "declared_features": "[\"default\", \"log_secure_cookie_values\", \"preserve_order\", \"public_suffix\", \"serde\", \"serde_json\", \"serde_ron\", \"wasm-bindgen\"]", "target": 8140962409157740669, "profile": 2040997289075261528, "path": 17266973371716140037, "deps": [[505596520502798227, "publicsuffix", false, 13694739885652373078], [3150220818285335163, "url", false, 11654752898637132681], [5986029879202738730, "log", false, 15905267756897252431], [6376232718484714452, "idna", false, 11709772997362015241], [9689903380558560274, "serde", false, 9841288889961963921], [11763018104473073732, "document_features", false, 6476781277268775314], [12409575957772518135, "time", false, 9630963353869857590], [15367738274754116744, "serde_json", false, 13225267877164477003], [16257276029081467297, "serde_derive", false, 5576570851292141867], [16727543399706004146, "cookie", false, 3188766063702275423]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\cookie_store-9886fe6996325726\\dep-lib-cookie_store", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}