# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-set-always-on-bottom"
description = "Enables the set_always_on_bottom command without any pre-configured scope."
commands.allow = ["set_always_on_bottom"]

[[permission]]
identifier = "deny-set-always-on-bottom"
description = "Denies the set_always_on_bottom command without any pre-configured scope."
commands.deny = ["set_always_on_bottom"]
