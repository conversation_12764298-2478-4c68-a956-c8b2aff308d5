{"rustc": 1842507548689473721, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"html-manipulation\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 1369601567987815722, "path": 4128467976108975410, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 9818158836129114236], [3060637413840920116, "proc_macro2", false, 5096312196297537516], [3150220818285335163, "url", false, 14894005264751682384], [3191507132440681679, "serde_untagged", false, 9960115931505257666], [4071963112282141418, "serde_with", false, 13334440874544442146], [4899080583175475170, "semver", false, 10438149423428263558], [5986029879202738730, "log", false, 104423116417116893], [6606131838865521726, "ctor", false, 10245730700901959920], [6913375703034175521, "schemars", false, 5220195266555608873], [7170110829644101142, "json_patch", false, 3898244098352474111], [8319709847752024821, "uuid", false, 14754163599292717405], [9010263965687315507, "http", false, 3662191618500152102], [9451456094439810778, "regex", false, 2525954231128846445], [9556762810601084293, "brotli", false, 14777250860789216788], [9689903380558560274, "serde", false, 101596170299597295], [10806645703491011684, "thiserror", false, 7518697846591293610], [11655476559277113544, "cargo_metadata", false, 18187771686460441922], [11989259058781683633, "dunce", false, 15719908978251450929], [13625485746686963219, "anyhow", false, 17279382658687598212], [14232843520438415263, "html5ever", false, 10758620899570308102], [15088007382495681292, "kuchiki", false, 1638861846132641802], [15367738274754116744, "serde_json", false, 10713812322005748657], [15609422047640926750, "toml", false, 900546928040642840], [15622660310229662834, "walkdir", false, 4267858985654455119], [15932120279885307830, "memchr", false, 8346766755315681689], [17146114186171651583, "infer", false, 5955301405455520504], [17155886227862585100, "glob", false, 12384589947144232082], [17186037756130803222, "phf", false, 15115045654389076670], [17990358020177143287, "quote", false, 9304775612473768952]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-utils-6addcda1374634dc\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}