{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[14039947826026167952, "build_script_build", false, 17367334986966622730], [6416823254013318197, "build_script_build", false, 14457086931255315033], [16171925541490437305, "build_script_build", false, 2250587051019741830]], "local": [{"RerunIfChanged": {"output": "release\\build\\tauri-plugin-http-6606f8dde2873b47\\output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}