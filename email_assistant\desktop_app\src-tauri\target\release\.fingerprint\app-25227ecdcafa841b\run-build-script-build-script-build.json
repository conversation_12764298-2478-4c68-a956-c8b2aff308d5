{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[1322478694103194923, "build_script_build", false, 11631288731047789616], [14039947826026167952, "build_script_build", false, 17367334986966622730], [1797035611096599003, "build_script_build", false, 7253554320989309662], [14525517306681678134, "build_script_build", false, 13774327749929021438], [6416823254013318197, "build_script_build", false, 14457086931255315033], [16171925541490437305, "build_script_build", false, 790388759466741103], [8324462083842905811, "build_script_build", false, 1934454518307701605]], "local": [{"RerunIfChanged": {"output": "release\\build\\app-25227ecdcafa841b\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}