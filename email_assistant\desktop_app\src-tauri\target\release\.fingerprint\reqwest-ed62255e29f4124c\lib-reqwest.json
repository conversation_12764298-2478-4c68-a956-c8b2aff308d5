{"rustc": 1842507548689473721, "features": "[\"__tls\", \"default\", \"default-tls\", \"hyper-tls\", \"json\", \"native-tls-crate\", \"serde_json\", \"tokio-native-tls\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 2040997289075261528, "path": 7565996450976943501, "deps": [[40386456601120721, "percent_encoding", false, 2129877517113298051], [95042085696191081, "ipnet", false, 5592815841625619349], [264090853244900308, "sync_wrapper", false, 15681497232691445498], [784494742817713399, "tower_service", false, 5062019019135106002], [1906322745568073236, "pin_project_lite", false, 2389057907062384012], [3150220818285335163, "url", false, 11654752898637132681], [3722963349756955755, "once_cell", false, 10802249507119394238], [4405182208873388884, "http", false, 13992638518803364567], [5986029879202738730, "log", false, 15905267756897252431], [7414427314941361239, "hyper", false, 2080796106033324028], [7620660491849607393, "futures_core", false, 4359839236934194262], [8405603588346937335, "winreg", false, 13852917898870302747], [8915503303801890683, "http_body", false, 5709245041612786252], [9689903380558560274, "serde", false, 9841288889961963921], [10229185211513642314, "mime", false, 9651018063964816897], [10629569228670356391, "futures_util", false, 12982142533534609786], [12186126227181294540, "tokio_native_tls", false, 4130736620769073756], [12367227501898450486, "hyper_tls", false, 2030677165574926896], [12393800526703971956, "tokio", false, 1417906539048028063], [13809605890706463735, "h2", false, 5104086491227303030], [14564311161534545801, "encoding_rs", false, 1044314460687697280], [15367738274754116744, "serde_json", false, 13225267877164477003], [16066129441945555748, "bytes", false, 5746740623261701503], [16311359161338405624, "rustls_pemfile", false, 9697493388661030122], [16542808166767769916, "serde_urlencoded", false, 1183088342104923807], [16785601910559813697, "native_tls_crate", false, 4629466734092853401], [18066890886671768183, "base64", false, 10462734816124331761]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\reqwest-ed62255e29f4124c\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}