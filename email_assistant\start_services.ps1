# AI-Assisted Email Response System - Service Startup Script
# This script starts all the required services for the email assistant system

Write-Host "🚀 Starting AI-Assisted Email Response System Services" -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor Green

# Function to check if a port is in use
function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $true
    }
    catch {
        return $false
    }
}

# Function to start a service in a new PowerShell window
function Start-ServiceWindow {
    param(
        [string]$Title,
        [string]$Command,
        [string]$WorkingDirectory,
        [int]$Port = 0
    )
    
    if ($Port -gt 0 -and (Test-Port $Port)) {
        Write-Host "✅ $Title is already running on port $Port" -ForegroundColor Yellow
        return
    }
    
    Write-Host "🔄 Starting $Title..." -ForegroundColor Cyan
    
    $startInfo = New-Object System.Diagnostics.ProcessStartInfo
    $startInfo.FileName = "powershell.exe"
    $startInfo.Arguments = "-NoExit -Command `"cd '$WorkingDirectory'; $Command`""
    $startInfo.WindowStyle = [System.Diagnostics.ProcessWindowStyle]::Normal
    $startInfo.CreateNoWindow = $false
    
    try {
        [System.Diagnostics.Process]::Start($startInfo) | Out-Null
        Write-Host "✅ $Title started successfully" -ForegroundColor Green
        Start-Sleep -Seconds 2
    }
    catch {
        Write-Host "❌ Failed to start $Title`: $_" -ForegroundColor Red
    }
}

# Check if Ollama is installed and add to PATH
$ollamaPath = "C:\Users\<USER>\AppData\Local\Programs\Ollama"
if (Test-Path "$ollamaPath\ollama.exe") {
    $env:PATH += ";$ollamaPath"
    Write-Host "✅ Ollama found and added to PATH" -ForegroundColor Green
} else {
    Write-Host "❌ Ollama not found. Please install Ollama first." -ForegroundColor Red
    exit 1
}

Write-Host ""

# Start Ollama service
Write-Host "1️⃣ Starting Ollama Service..." -ForegroundColor Blue
if (-not (Test-Port 11434)) {
    Start-ServiceWindow -Title "Ollama Service" -Command "ollama serve" -WorkingDirectory $PWD -Port 11434
} else {
    Write-Host "✅ Ollama is already running on port 11434" -ForegroundColor Yellow
}

Write-Host ""

# Start Ingestion Service
Write-Host "2️⃣ Starting Ingestion Service..." -ForegroundColor Blue
$ingestionPath = Join-Path $PWD "ingestion_service"
Start-ServiceWindow -Title "Ingestion Service" -Command "python main.py" -WorkingDirectory $ingestionPath -Port 8080

Write-Host ""

# Start RAG Service
Write-Host "3️⃣ Starting RAG Service..." -ForegroundColor Blue
$ragPath = Join-Path $PWD "rag_service"
Start-ServiceWindow -Title "RAG Service" -Command "python main.py" -WorkingDirectory $ragPath -Port 8003

Write-Host ""

# Wait for services to start
Write-Host "⏳ Waiting for services to initialize..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

Write-Host ""
Write-Host "🎯 Service Status Check:" -ForegroundColor Blue

# Check service status
$services = @(
    @{Name="Ollama"; Port=11434; URL="http://localhost:11434/api/tags"},
    @{Name="Ingestion Service"; Port=8080; URL="http://localhost:8080/health"},
    @{Name="RAG Service"; Port=8003; URL="http://localhost:8003/health"}
)

foreach ($service in $services) {
    try {
        $response = Invoke-WebRequest -Uri $service.URL -TimeoutSec 5 -ErrorAction Stop
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ $($service.Name) is running on port $($service.Port)" -ForegroundColor Green
        } else {
            Write-Host "⚠️ $($service.Name) responded with status $($response.StatusCode)" -ForegroundColor Yellow
        }
    }
    catch {
        Write-Host "❌ $($service.Name) is not responding on port $($service.Port)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "🏁 Service startup completed!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Test the RAG pipeline: python test_rag_pipeline.py" -ForegroundColor White
Write-Host "2. Start the desktop application:" -ForegroundColor White
Write-Host "   cd desktop_app" -ForegroundColor White
Write-Host "   npm run tauri dev" -ForegroundColor White
Write-Host ""
Write-Host "🌐 Service URLs:" -ForegroundColor Cyan
Write-Host "- Ollama: http://localhost:11434" -ForegroundColor White
Write-Host "- Ingestion Service: http://localhost:8080" -ForegroundColor White
Write-Host "- RAG Service: http://localhost:8003" -ForegroundColor White
Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
