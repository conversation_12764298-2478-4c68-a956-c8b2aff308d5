{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2040997289075261528, "path": 4128467976108975410, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 8092876893493829581], [3150220818285335163, "url", false, 11654752898637132681], [3191507132440681679, "serde_untagged", false, 3181594523022666681], [4071963112282141418, "serde_with", false, 12610372103844256500], [4899080583175475170, "semver", false, 11249359115376774636], [5986029879202738730, "log", false, 15905267756897252431], [6606131838865521726, "ctor", false, 10245730700901959920], [7170110829644101142, "json_patch", false, 17454258579547285587], [8319709847752024821, "uuid", false, 523096087748055006], [9010263965687315507, "http", false, 1862789691165185595], [9451456094439810778, "regex", false, 6287572008168542260], [9556762810601084293, "brotli", false, 11076486923476550830], [9689903380558560274, "serde", false, 9841288889961963921], [10806645703491011684, "thiserror", false, 13847597657789702417], [11989259058781683633, "dunce", false, 486707178400923159], [13625485746686963219, "anyhow", false, 2431689092402825966], [15367738274754116744, "serde_json", false, 13225267877164477003], [15609422047640926750, "toml", false, 16106376765397896577], [15622660310229662834, "walkdir", false, 17968052758206173863], [15932120279885307830, "memchr", false, 16574566946210894558], [17146114186171651583, "infer", false, 8316104060976983275], [17155886227862585100, "glob", false, 4801171762843958688], [17186037756130803222, "phf", false, 6328082251148980878]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-utils-7135570f425c4604\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}