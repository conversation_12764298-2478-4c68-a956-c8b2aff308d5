{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 2040997289075261528, "path": 3402796170130056749, "deps": [[2671782512663819132, "tauri_utils", false, 8187563711295407785], [3150220818285335163, "url", false, 11654752898637132681], [4143744114649553716, "raw_window_handle", false, 11946703729155000207], [6089812615193535349, "build_script_build", false, 1325125709251860666], [7606335748176206944, "dpi", false, 12077686462733247621], [9010263965687315507, "http", false, 1862789691165185595], [9689903380558560274, "serde", false, 9841288889961963921], [10806645703491011684, "thiserror", false, 13847597657789702417], [14585479307175734061, "windows", false, 3114519431610511065], [15367738274754116744, "serde_json", false, 13225267877164477003], [16727543399706004146, "cookie", false, 3188766063702275423]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-dd5b2029bf2f396a\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}