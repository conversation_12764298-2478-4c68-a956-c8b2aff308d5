{"rustc": 1842507548689473721, "features": "[\"http1\", \"http2\", \"ring\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "declared_features": "[\"aws-lc-rs\", \"default\", \"fips\", \"http1\", \"http2\", \"log\", \"logging\", \"native-tokio\", \"ring\", \"rustls-native-certs\", \"rustls-platform-verifier\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "target": 12220062926890100908, "profile": 2040997289075261528, "path": 632669836025513150, "deps": [[784494742817713399, "tower_service", false, 5062019019135106002], [2883436298747778685, "pki_types", false, 18021711389995360], [5907992341687085091, "webpki_roots", false, 14623930811583027819], [9010263965687315507, "http", false, 1862789691165185595], [11895591994124935963, "tokio_rustls", false, 414489368905162938], [11957360342995674422, "hyper", false, 5268801175711681447], [12393800526703971956, "tokio", false, 1417906539048028063], [16400140949089969347, "rustls", false, 1263988211099503875], [16680807377217054954, "hyper_util", false, 4595030664336176680]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\hyper-rustls-72317227d80ed314\\dep-lib-hyper_rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}