{"rustc": 1842507548689473721, "features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"charset\", \"cookies\", \"h2\", \"http2\", \"macos-system-configuration\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"system-proxy\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"system-proxy\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 7859547470675518382, "path": 12434675677363017947, "deps": [[40386456601120721, "percent_encoding", false, 2129877517113298051], [784494742817713399, "tower_service", false, 5062019019135106002], [1788832197870803419, "hyper_rustls", false, 17665698061313692397], [1906322745568073236, "pin_project_lite", false, 2389057907062384012], [2054153378684941554, "tower_http", false, 14582121991536575173], [2517136641825875337, "sync_wrapper", false, 8905282965693471066], [2883436298747778685, "rustls_pki_types", false, 18021711389995360], [3150220818285335163, "url", false, 11654752898637132681], [5695049318159433696, "tower", false, 11764452345109143343], [5907992341687085091, "webpki_roots", false, 14623930811583027819], [5986029879202738730, "log", false, 15905267756897252431], [7620660491849607393, "futures_core", false, 4359839236934194262], [8298091525883606470, "cookie_store", false, 4445271584190846920], [9010263965687315507, "http", false, 1862789691165185595], [9689903380558560274, "serde", false, 9841288889961963921], [10229185211513642314, "mime", false, 9651018063964816897], [11895591994124935963, "tokio_rustls", false, 414489368905162938], [11957360342995674422, "hyper", false, 5268801175711681447], [12393800526703971956, "tokio", false, 1417906539048028063], [13077212702700853852, "base64", false, 16878550808635762142], [14084095096285906100, "http_body", false, 15356474674297432158], [14359893265615549706, "h2", false, 13838384878900037229], [14564311161534545801, "encoding_rs", false, 1044314460687697280], [16066129441945555748, "bytes", false, 5746740623261701503], [16400140949089969347, "rustls", false, 1263988211099503875], [16542808166767769916, "serde_urlencoded", false, 1183088342104923807], [16680807377217054954, "hyper_util", false, 4595030664336176680], [16727543399706004146, "cookie_crate", false, 3188766063702275423], [16900715236047033623, "http_body_util", false, 7220491532981291408]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\reqwest-6ded5f4fa5d78ce2\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}